const axios = require('axios');

// Test the Stripe Connect endpoints
async function testStripeConnect() {
  const baseURL = 'http://localhost:5000/api/v1';
  
  // You'll need to replace this with a valid JWT token from a teacher account
  const token = 'YOUR_JWT_TOKEN_HERE';
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  try {
    console.log('Testing Stripe Connect account status...');
    
    // Test account status endpoint
    const statusResponse = await axios.get(`${baseURL}/stripe-connect/account-status`, {
      headers
    });
    
    console.log('Account Status Response:', statusResponse.data);
    
    // Test create account endpoint
    console.log('\nTesting Stripe Connect account creation...');
    
    const createAccountResponse = await axios.post(`${baseURL}/stripe-connect/create-account`, {
      type: 'express',
      country: 'US',
      email: '<EMAIL>',
      business_type: 'individual'
    }, {
      headers
    });
    
    console.log('Create Account Response:', createAccountResponse.data);
    
  } catch (error) {
    console.error('Error testing Stripe Connect:', error.response?.data || error.message);
  }
}

// Run the test
testStripeConnect();
