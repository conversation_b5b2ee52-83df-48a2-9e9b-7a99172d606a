"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeConnectController = void 0;
const http_status_1 = __importDefault(require("http-status"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const sendResponse_1 = __importDefault(require("../../utils/sendResponse"));
const stripeConnect_service_1 = require("./stripeConnect.service");
const AppError_1 = __importDefault(require("../../errors/AppError"));
// Create Stripe Connect account
const createAccount = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const teacherId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
    const { type, country, email, business_type } = req.body;
    if (!teacherId) {
        throw new AppError_1.default(http_status_1.default.UNAUTHORIZED, 'Teacher ID not found');
    }
    if (!type || !country || !email) {
        throw new AppError_1.default(http_status_1.default.BAD_REQUEST, 'Missing required fields: type, country, email');
    }
    const result = yield stripeConnect_service_1.StripeConnectService.createStripeAccount(teacherId, {
        type,
        country,
        email,
        business_type,
    });
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.CREATED,
        success: true,
        message: 'Stripe account created successfully',
        data: result,
    });
}));
// Create account link for onboarding
const createAccountLink = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const teacherId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
    const { type, refreshUrl, returnUrl } = req.body;
    if (!teacherId) {
        throw new AppError_1.default(http_status_1.default.UNAUTHORIZED, 'Teacher ID not found');
    }
    if (!type || !refreshUrl || !returnUrl) {
        throw new AppError_1.default(http_status_1.default.BAD_REQUEST, 'Missing required fields: type, refreshUrl, returnUrl');
    }
    const result = yield stripeConnect_service_1.StripeConnectService.createAccountLink(teacherId, {
        type,
        refreshUrl,
        returnUrl,
    });
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Account link created successfully',
        data: result,
    });
}));
// Get account status
const getAccountStatus = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const teacherId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
    if (!teacherId) {
        throw new AppError_1.default(http_status_1.default.UNAUTHORIZED, 'Teacher ID not found');
    }
    const result = yield stripeConnect_service_1.StripeConnectService.getAccountStatus(teacherId);
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Account status retrieved successfully',
        data: result,
    });
}));
// Update account information
const updateAccount = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const teacherId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
    const updateData = req.body;
    if (!teacherId) {
        throw new AppError_1.default(http_status_1.default.UNAUTHORIZED, 'Teacher ID not found');
    }
    const result = yield stripeConnect_service_1.StripeConnectService.updateAccount(teacherId, updateData);
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Account updated successfully',
        data: result,
    });
}));
// Disconnect account
const disconnectAccount = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const teacherId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
    if (!teacherId) {
        throw new AppError_1.default(http_status_1.default.UNAUTHORIZED, 'Teacher ID not found');
    }
    const result = yield stripeConnect_service_1.StripeConnectService.disconnectAccount(teacherId);
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Account disconnected successfully',
        data: result,
    });
}));
exports.StripeConnectController = {
    createAccount,
    createAccountLink,
    getAccountStatus,
    updateAccount,
    disconnectAccount,
};
