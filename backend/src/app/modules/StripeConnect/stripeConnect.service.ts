import Stripe from 'stripe';
import httpStatus from 'http-status';
import AppError from '../../errors/AppError';
import { Teacher } from '../Teacher/teacher.model';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2024-06-20',
});

// Create Stripe Connect account
const createStripeAccount = async (userId: string, accountData: {
  type: 'express' | 'standard';
  country: string;
  email: string;
  business_type?: 'individual' | 'company';
}) => {
  try {
    console.log('Creating Stripe account for user:', userId);

    // Check if teacher already has a Stripe account
    // Note: userId is the User._id, we need to find Teacher by user field
    const teacher = await Teacher.findOne({ user: userId });
    if (!teacher) {
      throw new AppError(httpStatus.NOT_FOUND, 'Teacher not found');
    }

    if (teacher.stripeAccountId) {
      throw new AppError(httpStatus.BAD_REQUEST, 'Teacher already has a Stripe account');
    }

    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: accountData.type,
      country: accountData.country,
      email: accountData.email,
      business_type: accountData.business_type || 'individual',
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      metadata: {
        teacherId: teacher._id.toString(),
        userId: userId,
        platform: 'LMS',
      },
    });

    // Update teacher with Stripe account ID
    await Teacher.findByIdAndUpdate(teacher._id, {
      stripeAccountId: account.id,
    });

    console.log('Stripe account created successfully:', account.id);

    return {
      accountId: account.id,
      isConnected: true,
      isVerified: account.details_submitted && account.charges_enabled,
      canReceivePayments: account.charges_enabled && account.payouts_enabled,
      requirements: account.requirements,
    };
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to create Stripe account: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

// Create account link for onboarding
const createAccountLink = async (userId: string, linkData: {
  type: 'account_onboarding' | 'account_update';
  refreshUrl: string;
  returnUrl: string;
}) => {
  try {
    const teacher = await Teacher.findOne({ user: userId });
    if (!teacher || !teacher.stripeAccountId) {
      throw new AppError(httpStatus.BAD_REQUEST, 'No Stripe account found for this teacher');
    }

    const accountLink = await stripe.accountLinks.create({
      account: teacher.stripeAccountId,
      refresh_url: linkData.refreshUrl,
      return_url: linkData.returnUrl,
      type: linkData.type,
    });

    return {
      url: accountLink.url,
      expiresAt: accountLink.expires_at,
    };
  } catch (error) {
    console.error('Error creating account link:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to create account link: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

// Get account status
const getAccountStatus = async (userId: string) => {
  try {
    const teacher = await Teacher.findOne({ user: userId });
    if (!teacher) {
      throw new AppError(httpStatus.NOT_FOUND, 'Teacher not found');
    }

    if (!teacher.stripeAccountId) {
      return {
        isConnected: false,
        isVerified: false,
        canReceivePayments: false,
        accountId: null,
        requirements: null,
      };
    }

    const account = await stripe.accounts.retrieve(teacher.stripeAccountId);

    return {
      isConnected: true,
      isVerified: account.details_submitted && account.charges_enabled,
      canReceivePayments: account.charges_enabled && account.payouts_enabled,
      accountId: account.id,
      requirements: account.requirements,
      capabilities: account.capabilities,
      country: account.country,
      defaultCurrency: account.default_currency,
      email: account.email,
      businessProfile: account.business_profile,
    };
  } catch (error) {
    console.error('Error getting account status:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to get account status: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

// Update account information
const updateAccount = async (userId: string, updateData: any) => {
  try {
    const teacher = await Teacher.findOne({ user: userId });
    if (!teacher || !teacher.stripeAccountId) {
      throw new AppError(httpStatus.BAD_REQUEST, 'No Stripe account found for this teacher');
    }

    const updatedAccount = await stripe.accounts.update(
      teacher.stripeAccountId,
      updateData
    );

    return {
      accountId: updatedAccount.id,
      businessProfile: updatedAccount.business_profile,
      capabilities: updatedAccount.capabilities,
      requirements: updatedAccount.requirements,
    };
  } catch (error) {
    console.error('Error updating account:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to update account: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

// Delete/disconnect account
const disconnectAccount = async (userId: string) => {
  try {
    const teacher = await Teacher.findOne({ user: userId });
    if (!teacher || !teacher.stripeAccountId) {
      throw new AppError(httpStatus.BAD_REQUEST, 'No Stripe account found for this teacher');
    }

    // Delete the Stripe account
    await stripe.accounts.del(teacher.stripeAccountId);

    // Remove Stripe account ID from teacher
    await Teacher.findByIdAndUpdate(teacher._id, {
      $unset: { stripeAccountId: 1 },
    });

    return { success: true, message: 'Stripe account disconnected successfully' };
  } catch (error) {
    console.error('Error disconnecting account:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to disconnect account: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

export const StripeConnectService = {
  createStripeAccount,
  createAccountLink,
  getAccountStatus,
  updateAccount,
  disconnectAccount,
};
